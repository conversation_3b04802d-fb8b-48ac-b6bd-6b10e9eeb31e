import { type NextRequest, NextResponse } from 'next/server';
import { getDatabaseConfig } from '@/lib/configCache';
import type { DatabaseConfig } from '@/lib/configCache';
import { getDynamicTable, validateDatabaseCode, isDrizzleTable } from '@/lib/drizzleTableMapping';
import { validatePaginationParams, buildPaginationResponse } from '@/lib/globalPagination';
import { buildAdvancedSearchWhere } from '@/lib/server/buildDrizzleWhere';
import { db } from '@/lib/db-server';
import { count, desc, asc, and } from 'drizzle-orm';

export const dynamic = 'force-dynamic';

interface SearchCondition {
  id: string;
  field: string;
  value: string | string[] | { from?: string; to?: string };
  logic?: 'AND' | 'OR' | 'NOT';
}

interface AdvancedSearchRequest {
  conditions: SearchCondition[];
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  legacyFilters?: Record<string, any>; // 支持传统的filter参数
}

// 注意：现在使用 buildAdvancedSearchWhere 从 buildDrizzleWhere.ts 处理所有条件构建

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  const startTime = Date.now();

  try {
    const { database: rawDatabase } = await params;
    
    // 统一转换为小写格式
    const database = rawDatabase.toLowerCase();

    // 验证数据库代码
    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: validation.status || 400 }
      );
    }

    // 权限检查 - 临时禁用用于测试
    /*
    const requiredLevel = getDatabaseAccessLevel(database);
    const hasAccess = await checkPermissions(requiredLevel);
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Permission denied' },
        { status: 403 }
      );
    }
    */

    const body: AdvancedSearchRequest = await request.json();
    const { conditions, sortBy, sortOrder = 'desc', legacyFilters = {} } = body;

    console.log('[Advanced Search API] 接收到的参数:', {
      conditions: conditions.length,
      legacyFilters: Object.keys(legacyFilters).length,
      sortBy,
      sortOrder
    });

    // 使用全局翻页配置（性能优化）
    const requestedPage = body.page || 1;
    const requestedLimit = body.limit || 0;

    const { page, limit } = validatePaginationParams(requestedPage, requestedLimit);

    // 获取配置
    const config: DatabaseConfig = await getDatabaseConfig(database);

    // 使用已获取的配置
    const visibleFields = config.fields.filter(f => f.isVisible).map(f => f.fieldName);
    const sortableFields = config.fields.filter(f => f.isSortable).map(f => f.fieldName);

    // 获取动态表
    const table = await getDynamicTable(database);
    if (!isDrizzleTable(table)) {
      return NextResponse.json(
        { success: false, error: '模型未找到或无效' },
        { status: 500 }
      );
    }

    // 构建复杂查询条件 - 使用Drizzle格式
    let where = buildAdvancedSearchWhere(conditions, config, table);

    // 如果有legacy filters，需要将其转换并合并到where条件中
    if (Object.keys(legacyFilters).length > 0) {
      console.log('[Advanced Search API] 处理legacy filters:', legacyFilters);

      // 将legacy filters转换为URLSearchParams格式
      const searchParams = new URLSearchParams();
      Object.entries(legacyFilters).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(key, String(v)));
        } else {
          searchParams.set(key, String(value));
        }
      });

      // 使用buildDrizzleWhere处理legacy filters
      const { buildDrizzleWhere } = await import('@/lib/server/buildDrizzleWhere');
      const legacyWhere = buildDrizzleWhere(searchParams, config, table);

      // 合并Advanced Search条件和legacy条件
      if (where && legacyWhere) {
        where = and(where, legacyWhere);
      } else if (legacyWhere) {
        where = legacyWhere;
      }
    }

    // 构建排序条件 - Drizzle格式
    const defaultSortField = sortableFields[0] || 'id';
    const sortField = sortBy || defaultSortField;
    const sortColumn = table[sortField];

    let orderByClause;
    if (sortColumn) {
      orderByClause = sortOrder === 'asc' ? [asc(sortColumn)] : [desc(sortColumn)];
    } else {
      // 回退到id排序
      orderByClause = [desc(table.id)];
    }

    // 计算总数
    const totalCountResult = await db
      .select({ count: count() })
      .from(table)
      .where(where);
    const totalCount = Number(totalCountResult[0]?.count || 0);

    // 查询数据
    const offset = (page - 1) * limit;

    let queryBuilder = db
      .select()
      .from(table);

    if (where) {
      queryBuilder = queryBuilder.where(where);
    }

    const data = await queryBuilder
      .orderBy(...orderByClause)
      .limit(limit)
      .offset(offset);

    // 过滤返回的字段，只返回可见字段
    const filteredData = data.map(item => {
      const filtered: Record<string, any> = { id: item.id };
      visibleFields.forEach(fieldName => {
        if (item.hasOwnProperty(fieldName)) {
          filtered[fieldName] = item[fieldName];
        }
      });
      return filtered;
    });

    return NextResponse.json({
      success: true,
      data: filteredData,
      pagination: buildPaginationResponse(page, limit, totalCount),
      config: {
        fields: config.fields,
        database: config.database,
      },
      search_info: {
        conditions_count: conditions.length,
        legacy_filters_count: Object.keys(legacyFilters).length,
        search_time: Date.now() - startTime,
      },
    });
    
  } catch (error) {
    console.error('Advanced search API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
