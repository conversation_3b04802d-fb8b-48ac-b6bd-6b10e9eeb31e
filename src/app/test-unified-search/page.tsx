'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Search, Database, Clock, Target } from 'lucide-react';

interface GlobalSearchResult {
  database: string;
  count: number;
}

interface DatabaseSearchResult {
  data: unknown[];
  pagination: {
    page: number;
    limit: number;
    total_pages: number;
    total_results: number;
  };
  search_info: {
    query: string;
    search_time: number;
    es_took: number;
    es_total?: number;
    prisma_total?: number;
    missing_ids_count: number;
  };
}

export default function TestUnifiedSearchPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDatabase, setSelectedDatabase] = useState('us_class');
  const [loading, setLoading] = useState({
    global: false,
    database: false
  });
  
  const [globalResults, setGlobalResults] = useState<{
    success: boolean;
    data: GlobalSearchResult[];
    search_info?: any;
    error?: string;
  } | null>(null);
  
  const [databaseResults, setDatabaseResults] = useState<{
    success: boolean;
    data?: DatabaseSearchResult;
    error?: string;
  } | null>(null);

  // 测试全站搜索
  const testGlobalSearch = async () => {
    if (!searchQuery.trim()) return;
    
    setLoading(prev => ({ ...prev, global: true }));
    try {
      const response = await fetch(`/api/unified-global-search?q=${encodeURIComponent(searchQuery)}`);
      const result = await response.json();
      setGlobalResults(result);
    } catch (error) {
      console.error('全站搜索失败:', error);
      setGlobalResults({
        success: false,
        data: [],
        error: error instanceof Error ? error.message : '网络错误'
      });
    } finally {
      setLoading(prev => ({ ...prev, global: false }));
    }
  };

  // 测试数据库搜索
  const testDatabaseSearch = async () => {
    if (!searchQuery.trim()) return;
    
    setLoading(prev => ({ ...prev, database: true }));
    try {
      const response = await fetch(`/api/advanced-search/${selectedDatabase}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          conditions: [{
            id: 'global_search',
            field: 'allFields',
            value: searchQuery,
            logic: undefined
          }],
          page: 1,
          limit: 10
        }),
      });
      if (response.ok) {
        const result = await response.json();

        if (result.success) {
          setDatabaseResults({
            success: true,
            data: {
              data: result.data,
              pagination: result.pagination,
              search_info: { query: searchQuery, search_time: 0 } // 简化的search_info
            }
          });
        } else {
          setDatabaseResults(result);
        }
      } else {
        const errorResult = await response.json();
        setDatabaseResults(errorResult);
      }
    } catch (error) {
      console.error('数据库搜索失败:', error);
      setDatabaseResults({
        success: false,
        error: error instanceof Error ? error.message : '网络错误'
      });
    } finally {
      setLoading(prev => ({ ...prev, database: false }));
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      testGlobalSearch();
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">统一搜索系统测试</h1>
        <p className="text-gray-600">
          测试新的ES + Prisma统一搜索架构：ES负责检索，Prisma负责回捞和二次筛选
        </p>
      </div>

      {/* 搜索输入区域 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            搜索测试
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-4">
            <Input
              placeholder="输入搜索关键词..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1"
            />
            <Input
              placeholder="数据库代码"
              value={selectedDatabase}
              onChange={(e) => setSelectedDatabase(e.target.value)}
              className="w-32"
            />
          </div>
          
          <div className="flex gap-2">
            <Button 
              onClick={testGlobalSearch} 
              disabled={loading.global || !searchQuery.trim()}
              className="flex items-center gap-2"
            >
              <Database className="h-4 w-4" />
              {loading.global ? '搜索中...' : '全站搜索'}
            </Button>
            <Button 
              onClick={testDatabaseSearch} 
              disabled={loading.database || !searchQuery.trim()}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Target className="h-4 w-4" />
              {loading.database ? '搜索中...' : '数据库搜索'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 结果展示 */}
      <Tabs defaultValue="global" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="global">全站搜索结果</TabsTrigger>
          <TabsTrigger value="database">数据库搜索结果</TabsTrigger>
        </TabsList>

        {/* 全站搜索结果 */}
        <TabsContent value="global">
          <Card>
            <CardHeader>
              <CardTitle>全站搜索结果</CardTitle>
              {globalResults?.search_info && (
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    搜索耗时: {globalResults.search_info.search_time?.toFixed(2)}ms
                  </div>
                  <div>ES耗时: {globalResults.search_info.es_took}ms</div>
                </div>
              )}
            </CardHeader>
            <CardContent>
              {globalResults ? (
                globalResults.success ? (
                  <div className="space-y-4">
                    {globalResults.data.length > 0 ? (
                      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {globalResults.data.map((result) => (
                          <Card key={result.database} className="p-4">
                            <div className="flex justify-between items-center">
                              <h4 className="font-medium">{result.database}</h4>
                              <Badge variant="secondary">
                                {result.count.toLocaleString()}
                              </Badge>
                            </div>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 text-center py-8">没有找到匹配的结果</p>
                    )}
                  </div>
                ) : (
                  <div className="text-red-600 p-4 bg-red-50 rounded">
                    <strong>错误:</strong> {globalResults.error}
                  </div>
                )
              ) : (
                <p className="text-gray-500 text-center py-8">点击"全站搜索"开始测试</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 数据库搜索结果 */}
        <TabsContent value="database">
          <Card>
            <CardHeader>
              <CardTitle>数据库搜索结果 ({selectedDatabase})</CardTitle>
              {databaseResults?.data?.search_info && (
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    搜索耗时: {databaseResults.data.search_info.search_time?.toFixed(2)}ms
                  </div>
                  <div>ES耗时: {databaseResults.data.search_info.es_took}ms</div>
                  {databaseResults.data.search_info.es_total && (
                    <div>ES命中: {databaseResults.data.search_info.es_total}</div>
                  )}
                  {databaseResults.data.search_info.prisma_total && (
                    <div>Prisma返回: {databaseResults.data.search_info.prisma_total}</div>
                  )}
                  {databaseResults.data.search_info.missing_ids_count > 0 && (
                    <Badge variant="destructive">
                      缺失ID: {databaseResults.data.search_info.missing_ids_count}
                    </Badge>
                  )}
                </div>
              )}
            </CardHeader>
            <CardContent>
              {databaseResults ? (
                databaseResults.success ? (
                  <div className="space-y-4">
                    {databaseResults.data?.data && databaseResults.data.data.length > 0 ? (
                      <>
                        <div className="text-sm text-gray-600 mb-4">
                          显示 {databaseResults.data.data.length} 条结果，
                          共 {databaseResults.data.pagination.total_results} 条
                        </div>
                        <div className="space-y-2">
                          {databaseResults.data.data.slice(0, 5).map((item: any, index) => (
                            <Card key={index} className="p-3">
                              <div className="text-sm">
                                <div className="font-medium mb-1">ID: {item.id}</div>
                                {item.product_combined && (
                                  <div className="text-gray-600">产品: {item.product_combined}</div>
                                )}
                                {item.company_combined && (
                                  <div className="text-gray-600">公司: {item.company_combined}</div>
                                )}
                                {item.registration_no && (
                                  <div className="text-gray-600">注册号: {item.registration_no}</div>
                                )}
                              </div>
                            </Card>
                          ))}
                        </div>
                        {databaseResults.data.data.length > 5 && (
                          <p className="text-gray-500 text-center">
                            还有 {databaseResults.data.data.length - 5} 条结果未显示...
                          </p>
                        )}
                      </>
                    ) : (
                      <p className="text-gray-500 text-center py-8">没有找到匹配的结果</p>
                    )}
                  </div>
                ) : (
                  <div className="text-red-600 p-4 bg-red-50 rounded">
                    <strong>错误:</strong> {databaseResults.error}
                  </div>
                )
              ) : (
                <p className="text-gray-500 text-center py-8">点击"数据库搜索"开始测试</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 调试信息 */}
      {(globalResults || databaseResults) && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>调试信息</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="global-debug">
              <TabsList>
                <TabsTrigger value="global-debug">全站搜索</TabsTrigger>
                <TabsTrigger value="database-debug">数据库搜索</TabsTrigger>
              </TabsList>
              
              <TabsContent value="global-debug">
                <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96">
                  {JSON.stringify(globalResults, null, 2)}
                </pre>
              </TabsContent>
              
              <TabsContent value="database-debug">
                <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96">
                  {JSON.stringify(databaseResults, null, 2)}
                </pre>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
